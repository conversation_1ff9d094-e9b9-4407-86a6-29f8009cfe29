package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"payment-backend/internal/config"
	"payment-backend/internal/domain"
	"payment-backend/internal/logger"
	"payment-backend/internal/middleware"
)

// OrderHandler 订单处理器
type OrderHandler struct {
	orderService domain.OrderService
	logger       logger.Logger
	config       *config.Config
}

// NewOrderHandler 创建订单处理器
func NewOrderHandler(orderService domain.OrderService, logger logger.Logger, config *config.Config) *OrderHandler {
	return &OrderHandler{
		orderService: orderService,
		logger:       logger,
		config:       config,
	}
}

// CreateOrder 创建订单
// @Summary 创建订单
// @Description 创建新的订单并生成支付链接，需要用户认证
// @Tags 订单管理
// @Accept json
// @Produce json
// @Param x-user-id header string true "用户ID" example("user123")
// @Param x-role header string true "用户角色" example("customer")
// @Param request body domain.CreateOrderRequest true "创建订单请求"
// @Success 303 {object} domain.CreateOrderResponse "订单创建成功，返回支付链接"
// @Failure 400 {object} domain.ErrorResponse "请求参数错误"
// @Failure 401 {object} domain.ErrorResponse "未授权，缺少认证信息"
// @Failure 500 {object} domain.ErrorResponse "服务器内部错误"
// @Router /api/v1/order-service/orders [post]
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	// 获取用户上下文
	userContext, err := middleware.MustGetUserContext(c)
	if err != nil {
		h.logger.Warn("Invalid user context type", logger.Error(err))
		c.JSON(http.StatusUnauthorized, domain.NewErrorResponse(domain.ErrCodeInvalidRequest, domain.MsgUserContext))
		return
	}

	// 解析请求
	var req domain.CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Failed to bind request", zap.Error(err))
		c.JSON(http.StatusBadRequest, domain.NewErrorResponse(domain.ErrCodeInvalidRequest, domain.MsgInvalidRequest+err.Error()))
		return
	}

	// 创建订单
	response, err := h.orderService.CreateOrder(userContext, &req)
	if err != nil {
		h.logger.Error("Failed to create order", zap.Error(err))
		c.JSON(http.StatusInternalServerError, domain.NewErrorResponse(domain.ErrCodeCreateOrderFailed, "Failed to create order."+err.Error()))
		return
	}

	c.JSON(http.StatusSeeOther, response)
}

// GetOrder 获取订单详情
func (h *OrderHandler) GetOrder(c *gin.Context) {
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Order ID is required"})
		return
	}

	order, err := h.orderService.GetOrder(orderID)
	if err != nil {
		h.logger.Error("Failed to get order", zap.String("order_id", orderID), zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// GetOrderByID 根据数据库ID获取订单
func (h *OrderHandler) GetOrderByID(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID is required"})
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	order, err := h.orderService.GetOrderByID(id)
	if err != nil {
		h.logger.Error("Failed to get order by ID", zap.Uint64("id", id), zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// GetUserOrders 获取用户订单列表
func (h *OrderHandler) GetUserOrders(c *gin.Context) {
	// 获取用户上下文
	userContext, err := middleware.MustGetUserContext(c)
	if err != nil {
		h.logger.Error("Invalid user context type")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user context"})
		return
	}

	// 解析分页参数
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// 获取订单列表
	orders, err := h.orderService.GetUserOrders(userContext.UserID, limit, offset)
	if err != nil {
		h.logger.Error("Failed to get user orders",
			zap.String("user_id", userContext.UserID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"orders": orders,
		"limit":  limit,
		"offset": offset,
	})
}

// ListAllOrders 获取所有订单列表（管理员接口）
func (h *OrderHandler) ListAllOrders(c *gin.Context) {
	// 获取分页配置
	defaultLimit := h.config.Admin.Pagination.DefaultLimit
	maxLimit := h.config.Admin.Pagination.MaxLimit

	// 解析分页参数
	limitStr := c.DefaultQuery("limit", strconv.Itoa(defaultLimit))
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > maxLimit {
		limit = defaultLimit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	pagination := &domain.PaginationRequest{
		Limit:  limit,
		Offset: offset,
	}

	// 解析过滤条件
	filter := &domain.OrderFilter{}

	// 用户ID过滤
	if userID := c.Query("user_id"); userID != "" {
		filter.UserID = &userID
	}

	// 货币过滤
	if currency := c.Query("currency"); currency != "" {
		filter.Currency = &currency
	}

	// 支付状态过滤
	if payStatus := c.Query("pay_status"); payStatus != "" {
		filter.PayStatus = &payStatus
	}

	// 支付方式过滤
	if payedMethod := c.Query("payed_method"); payedMethod != "" {
		filter.PayedMethod = &payedMethod
	}

	// PSP提供商过滤
	if pspProvider := c.Query("psp_provider"); pspProvider != "" {
		filter.PSPProvider = &pspProvider
	}

	// 支付时间范围过滤
	if payedAtStart := c.Query("payed_at_start"); payedAtStart != "" {
		if t, err := time.Parse(time.RFC3339, payedAtStart); err == nil {
			filter.PayedAtStart = &t
		}
	}
	if payedAtEnd := c.Query("payed_at_end"); payedAtEnd != "" {
		if t, err := time.Parse(time.RFC3339, payedAtEnd); err == nil {
			filter.PayedAtEnd = &t
		}
	}

	// 退款状态过滤
	if refundStatus := c.Query("refund_status"); refundStatus != "" {
		filter.RefundStatus = &refundStatus
	}

	// 退款时间范围过滤
	if refundedAtStart := c.Query("refunded_at_start"); refundedAtStart != "" {
		if t, err := time.Parse(time.RFC3339, refundedAtStart); err == nil {
			filter.RefundedAtStart = &t
		}
	}
	if refundedAtEnd := c.Query("refunded_at_end"); refundedAtEnd != "" {
		if t, err := time.Parse(time.RFC3339, refundedAtEnd); err == nil {
			filter.RefundedAtEnd = &t
		}
	}

	// PSP价格ID过滤
	if pspPriceID := c.Query("psp_price_id"); pspPriceID != "" {
		filter.PSPPriceID = &pspPriceID
	}

	// PSP客户邮箱过滤
	if pspCustomerEmail := c.Query("psp_customer_email"); pspCustomerEmail != "" {
		filter.PSPCustomerEmail = &pspCustomerEmail
	}

	// PSP订阅ID过滤
	if pspSubscriptionID := c.Query("psp_subscription_id"); pspSubscriptionID != "" {
		filter.PSPSubscriptionID = &pspSubscriptionID
	}

	// 调用服务层
	response, err := h.orderService.ListAllOrders(filter, pagination)
	if err != nil {
		h.logger.Error("Failed to list all orders",
			zap.Any("filter", filter),
			zap.Any("pagination", pagination),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// UpdateOrder 更新订单
func (h *OrderHandler) UpdateOrder(c *gin.Context) {
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Order ID is required"})
		return
	}

	// 解析请求
	var req domain.UpdateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 更新订单
	if err := h.orderService.UpdateOrder(orderID, &req); err != nil {
		h.logger.Error("Failed to update order", zap.String("order_id", orderID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Order updated successfully"})
}

// CancelOrder 取消订单
func (h *OrderHandler) CancelOrder(c *gin.Context) {
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Order ID is required"})
		return
	}

	if err := h.orderService.CancelOrder(orderID); err != nil {
		h.logger.Error("Failed to cancel order", zap.String("order_id", orderID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Order cancelled successfully"})
}

// RefundOrder 退款订单
func (h *OrderHandler) RefundOrder(c *gin.Context) {
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Order ID is required"})
		return
	}

	// 解析退款金额（可选）
	var req struct {
		Amount *float64 `json:"amount,omitempty"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.orderService.RefundOrder(orderID, req.Amount); err != nil {
		h.logger.Error("Failed to refund order", zap.String("order_id", orderID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Order refunded successfully"})
}

// ProcessWebhook 处理支付网关 stripe 的 webhook
func (h *OrderHandler) ProcessWebhookStripe(c *gin.Context) {
	// 获取请求体
	payload, err := c.GetRawData()
	if err != nil {
		h.logger.Error("Failed to get request body", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read request body"})
		return
	}

	// 获取签名等 HEADER
	httpHeader := c.Request.Header

	// 处理webhook
	if err := h.orderService.ProcessWebhook(domain.PSPProviderStripe, httpHeader, payload); err != nil {
		h.logger.Error("Failed to process webhook stripe",
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Webhook processed successfully"})
}

// HealthCheck 健康检查
func (h *OrderHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"service": "order-service",
	})
}
