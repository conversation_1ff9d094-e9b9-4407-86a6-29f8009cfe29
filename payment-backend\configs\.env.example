# Payment Backend Environment Variables Example
# 支付后端环境变量配置示例

# 服务器配置
PAYMENT_SERVER_HOST=0.0.0.0
PAYMENT_SERVER_PORT=8080
PAYMENT_SERVER_READ_TIMEOUT=30
PAYMENT_SERVER_WRITE_TIMEOUT=30
PAYMENT_SERVER_MODE=debug

# 数据库配置
PAYMENT_DATABASE_DRIVER=postgres
PAYMENT_DATABASE_HOST=localhost
PAYMENT_DATABASE_PORT=5432
PAYMENT_DATABASE_USERNAME=payment_user
PAYMENT_DATABASE_PASSWORD=payment_password
PAYMENT_DATABASE_DATABASE=payment_db
PAYMENT_DATABASE_SSL_MODE=disable

# Stripe 支付配置
PAYMENT_PAYMENT_PROVIDERS_STRIPE_ENABLED=true
PAYMENT_PAYMENT_PROVIDERS_STRIPE_API_KEY=sk_test_xxxxxxxxxxxxxxxxxxxxx
PAYMENT_PAYMENT_PROVIDERS_STRIPE_SECRET_KEY=sk_test_secret_xxxxxxxxxxxxx
PAYMENT_PAYMENT_PROVIDERS_STRIPE_WEBHOOK_URL=https://your-domain.com/api/v1/pay-service/webhooks/stripe
PAYMENT_PAYMENT_PROVIDERS_STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxxxxxxxx
PAYMENT_PAYMENT_PROVIDERS_STRIPE_SETTINGS_ENVIRONMENT=test

# PayPal 支付配置
PAYMENT_PAYMENT_PROVIDERS_PAYPAL_ENABLED=true
PAYMENT_PAYMENT_PROVIDERS_PAYPAL_API_KEY=your_paypal_api_key
PAYMENT_PAYMENT_PROVIDERS_PAYPAL_SECRET_KEY=your_paypal_secret_key
PAYMENT_PAYMENT_PROVIDERS_PAYPAL_WEBHOOK_URL=https://your-domain.com/api/v1/pay-service/webhooks/paypal
PAYMENT_PAYMENT_PROVIDERS_PAYPAL_WEBHOOK_SECRET=your_paypal_webhook_secret
PAYMENT_PAYMENT_PROVIDERS_PAYPAL_SETTINGS_ENVIRONMENT=sandbox

# 日志配置
PAYMENT_LOG_LEVEL=info
PAYMENT_LOG_FORMAT=json
PAYMENT_LOG_OUTPUT=stdout
PAYMENT_LOG_FILENAME=logs/payment-backend.log
PAYMENT_LOG_MAX_SIZE=100
PAYMENT_LOG_MAX_BACKUPS=3
PAYMENT_LOG_MAX_AGE=28

# 生产环境示例
# PAYMENT_SERVER_MODE=release
# PAYMENT_LOG_LEVEL=warn
# PAYMENT_LOG_FORMAT=json
# PAYMENT_PAYMENT_PROVIDERS_STRIPE_SETTINGS_ENVIRONMENT=live
# PAYMENT_PAYMENT_PROVIDERS_PAYPAL_SETTINGS_ENVIRONMENT=live

# 雪花算法配置
PAYMENT_SNOWFLAKE_NODE_ID=-1

# 管理员配置
PAYMENT_ADMIN_ALLOWED_ROLES=admin,super_admin
PAYMENT_ADMIN_PAGINATION_DEFAULT_LIMIT=50
PAYMENT_ADMIN_PAGINATION_MAX_LIMIT=500


