package app

import (
	"context"

	"go.uber.org/fx"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"payment-backend/internal/config"
	"payment-backend/internal/db"
	"payment-backend/internal/domain"
	dubboHandler "payment-backend/internal/dubbo/handler"
	dubboServer "payment-backend/internal/dubbo/server"
	"payment-backend/internal/handler"
	"payment-backend/internal/logger"
	"payment-backend/internal/repository/mysql"
	"payment-backend/internal/server"
	"payment-backend/internal/service"
	"payment-sdk/payment"
	paymentstripe "payment-sdk/payment-stripe"
)

// ConfigModule 配置模块
var ConfigModule = fx.Module("config",
	fx.Provide(func() (*config.Config, error) {
		return config.LoadConfig("")
	}),
)

// LoggerModule 日志模块
var LoggerModule = fx.Module("logger",
	fx.Provide(func(cfg *config.Config) (logger.Logger, error) {
		return logger.NewLogger(&cfg.Log)
	}),
)

// DatabaseModule 数据库模块
var DatabaseModule = fx.Module("database",
	fx.Provide(func(cfg *config.Config) (*gorm.DB, error) {
		// 初始化雪花算法
		if cfg.Snowflake.NodeID < 0 {
			// abort
			panic("snowflake node id must be greater than 0")
		}
		if err := db.InitSnowflake(cfg.Snowflake.NodeID); err != nil {
			return nil, err
		}

		// 初始化数据库连接
		if err := db.Init(&cfg.Database); err != nil {
			return nil, err
		}

		// 执行数据库迁移
		if err := db.Migrate(db.GetDB()); err != nil {
			return nil, err
		}

		return db.GetDB(), nil
	}),
)

// RepositoryModule 仓储模块
var RepositoryModule = fx.Module("repository",
	fx.Provide(
		fx.Annotate(
			mysql.NewOrderRepository,
			fx.As(new(domain.OrderRepository)),
		),
	),
)

// GatewayModule 网关模块
var GatewayModule = fx.Module("gateway",
	fx.Provide(
		func(cfg *config.Config, logger logger.Logger) map[string]payment.PaymentGateway {
			gatewayConfig := &payment.GatewayConfig{
				SecretKey: cfg.Payment.Providers["stripe"].APIKey,
			}
			return map[string]payment.PaymentGateway{
				"stripe": paymentstripe.NewPaymentGatewayStripe(gatewayConfig, logger.GetZapLogger()),
			}
		},
	),
)

// ServiceModule 服务模块
var ServiceModule = fx.Module("service",
	fx.Provide(
		fx.Annotate(
			func(orderRepo domain.OrderRepository, gateways map[string]payment.PaymentGateway, cfg *config.Config, logger logger.Logger) domain.OrderService {
				return service.NewOrderService(orderRepo, gateways, cfg, logger)
			},
			fx.As(new(domain.OrderService)),
		),
	),
)

// HandlerModule 处理器模块
var HandlerModule = fx.Module("handler",
	fx.Provide(
		func(orderService domain.OrderService, logger logger.Logger, cfg *config.Config) *handler.OrderHandler {
			return handler.NewOrderHandler(orderService, logger, cfg)
		},
	),
)

// DubboModule Dubbo模块
var DubboModule = fx.Module("dubbo",
	fx.Provide(
		func(orderService domain.OrderService, logger logger.Logger) *dubboHandler.OrderDubboHandler {
			return dubboHandler.NewOrderDubboHandler(orderService, logger)
		},
	),
	fx.Provide(
		func(cfg *config.Config, orderDubboHandler *dubboHandler.OrderDubboHandler, logger logger.Logger) (*dubboServer.DubboServer, error) {
			if !cfg.Dubbo.Enabled {
				return nil, nil // 如果未启用Dubbo，返回nil
			}
			return dubboServer.NewDubboServer(cfg, orderDubboHandler, logger)
		},
	),
	fx.Invoke(func(lc fx.Lifecycle, dubboSrv *dubboServer.DubboServer, logger logger.Logger) {
		if dubboSrv == nil {
			logger.Info("Dubbo server is disabled")
			return
		}
		lc.Append(fx.Hook{
			OnStart: func(ctx context.Context) error {
				go func() {
					if err := dubboSrv.Start(); err != nil {
						logger.Error("Dubbo server start failed", zap.Error(err))
					}
				}()
				return nil
			},
			OnStop: func(ctx context.Context) error {
				return dubboSrv.Stop(ctx)
			},
		})
	}),
)

// AppModule 应用模块
var AppModule = fx.Module("app",
	ConfigModule,
	LoggerModule,
	DatabaseModule,
	RepositoryModule,
	GatewayModule,
	ServiceModule,
	HandlerModule,
	DubboModule,
	server.ServerModule,
)

// NewApp 创建应用
func NewApp() *fx.App {
	return fx.New(
		AppModule,
		fx.NopLogger, // 禁用fx的默认日志，使用我们自己的日志
	)
}
