# 国际支付调研

[TOC]

## 浅谈国际支付

### 什么是国际支付

区别于大家常用的微信支付/支付宝支付, 国际支付是指在国外的用户通过用户国家支持的APP/网站进行付款, 付款的货币是美元/欧元等国际货币, 而不是人民币。

那如果大家出国，在超市付款时会发现很多超市支持微信/支付宝付款，那是为什么呢？背后的原理又是什么？

另外，外国人自己平时支付时，又会使用什么样的方式来付款呢？支付系统又该怎么收款呢？

### 支付系统

海外超市/商店支持微信/支付宝付款，那是为了迎合中国游客的支付习惯和促进消费，因为中国人更偏好用熟悉的移动支付方式，也就大家常听到的"我扫你还是你扫我"。 

海外商户不直接对接微信/支付宝, 而是通过一个本地 PSP（支付服务提供商）。PSP 会将微信/支付宝支付接口集成进 POS 系统、扫码台等。

因为微信/支付宝是基于人民币的，而商户并不是直接以人民币收款。所以商户通过 PSP 收到以美元、欧元等本地货币结算的款项, PSP 会承担汇率转换和手续费。

### 支付系统的重要性

如果在做海外业务，也就是把产品销售到国外让外国人用，那么国际的支付系统就必不可少了。

以下是美国主流支付方式的粗略统计，

| 支付方式                      | 使用占比（大致）               | 特点                       |
| ------------------------- | ---------------------- | ------------------------ |
| 💳 信用卡/借记卡                | 65%+                   | Visa/MasterCard/Amex 占主导 |
| 🟡 PayPal 钱包              | \~49%                  | 大量消费者已注册并信任              |
| 🍎 Apple Pay / Google Pay | 迅速增长中                  | 特别是在移动端                  |
| 📦 BNPL（分期）               | Klarna、Affirm、Afterpay | 年轻人偏好                    |

以下是部分欧洲国家主流支付方式的粗略统计，

| 国家              | 本地支付偏好                           |
| --------------- | -------------------------------- |
| 🇩🇪 德国         | SEPA Direct Debit、PayPal、Giropay |
| 🇳🇱 荷兰         | iDEAL（> 60% 市场）                  |
| 🇫🇷 法国         | Cartes Bancaires（银行卡）+ Apple Pay |
| 🇧🇪 比利时        | Bancontact                       |
| 🇬🇧 英国         | 信用卡、Apple Pay、PayPal             |
| 🇸🇪 瑞典/Nordics | Klarna（分期）、Swish                 |

我们可以看到做国际支付时，用户的支付方式复杂多样，我们没有那么多时间研究如此多的支付方式。那此时 PSP 的重要性应运而生，它让我们系统做一次集成，便支持所有这些本地方式。但是需要付一点交易费用。

### PSP 的选择

作为一个付费的互联网应用系统，除了核心业务做好做精以外，初期就需要做好的我认为就是升级模块和支付模块。

升级模块，无需多言，APP 出了问题如果升级不上去意味着已有用户可能都会失去。

支付模块，用户体验了我们辛辛苦苦开发的 APP 打算付款长期使用了，发现付款不了、或者是付款了我们后台收不到款项、亦或是搭建的后台服务被攻击了使得本没有付款的用户也被篡改成了付费用户...

如此看搭建一个对接 PSP 的支付模块并不是一件容易的事，因为它关系着实实在在的钱。

幸好，目前有一些很优秀的 PSP。PSP 很多，作者精力有限只调研了少量的 PSP。我们每个产品不一定要集成很多 PSP，可以在有限的时间和人力前提下先集成一款，发布之后再集成多个 PSP。

因为是在做涉及货币支付的国际业务，我们在考察 PSP 时，**还需要考虑到税务、发票和合规问题，这些是企业税务和法务部门需要重点关注的点**。否则，在海外遇到相关的投诉，容易被处罚和下架。

## PSP 初步对比

### 2025 年主流第三方国际支付平台速览

> 符号说明：💳 信用卡 🔄 订阅/循环扣费 🌍 多币种/本地化 🛡 风控/3DS
费率为公开起步价（具体以谈判结果为准）。

| 平台                               | 特点                                                                                                 | 适合场景                    |
| -------------------------------- | -------------------------------------------------------------------------------------------------- | ----------------------- |
| **Stripe** 💳🔄🌍🛡              | 135+ 货币、100+ 支付方式、开发者友好，内置 Billing、Tax、Radar。跨境卡 +0.5%，汇率+2%。 ([wise.com][1])                      | SaaS / 订阅型产品；技术团队可自定义流程 |
| **Adyen** 💳🌍🛡                 | 直接收单+全球本地支付，单一平台号称 99.9% 正常运行时间；费率按量议价。                                                            | 中高交易规模、需线下 + 线上统一的企业    |
| **Checkout.com** 💳🌍🛡          | 150+ 国、开箱就带多 PSP 路由与实时监控，支持加密货币支付。                                                                 | 跨境电商、金融科技，需要“自定义路由”     |
| **Braintree（PayPal）** 💳🔄🌍     | 同时拿到 PayPal & Venmo 钱包流量，自动网络 Token 续期。                                                            | 北美/欧洲面向个人消费者付费的业务       |
| **Worldpay / FIS** 💳🌍🛡        | 银行系，全球商户号覆盖面大，适合高量 + 实体卡分期。                                                                        | OTAs、订阅加实体分期混合模式        |
| **Square** 💳🌍                  | 线上 + POS 一体，但仅支持 8 个主要国家。                                                                          | 线上业务同时有线下小店铺            |
| **Airwallex / Wise Platform** 🌍 | 多币种收款 → 全币种企业账户，FX 费低，结汇灵活。 ([wise.com][1])                                                        | 小团队想把“收 + 汇 + 付”一站搞定    |
| **区域 PSP**                       | Razorpay (🇮🇳)、PayU (🇮🇳/LATAM)、DLocal (Emerging)、Flutterwave/Paystack (🇳🇬/🇿🇦) 等，深耕当地钱包/银行转账 | 目标市场在新兴国家，需本地付款方式       |
| **Merchant of Record**           | Paddle / FastSpring / LemonSqueezy / 2Checkout：官方成为卖家→代收、代缴 VAT/Sales Tax、代处理拒付                    | 想快速上线订阅，且不想碰税务 & PCI    |
| **Open Banking / 直扣**            | GoCardless（ACH/SEPA）、Klarna、iDEAL、BLIK…                                                            | 欧洲或需自动扣款的会员服务           |


[1]: https://wise.com/au/blog/international-payment-gateway-providers "7 Best International Payment Gateway Providers | 2025 - Wise"


**分类**

我们根据 PSP 扮演的角色将国际 PSP 简单的分为以下两类：

- Paddle = 全托管式，帮你当商户、缴税、开票、扛拒付（你像是在用淘宝/微信收款）

- Stripe Billing + Stripe Tax = 半托管式，你是商户，Stripe 仅提供收款 + 税务工具（你得像“独立站”一样对接账务和合规）

**我理解，全托管式 PSP 是你的商品销售合约托管给平台，而半托管式 PSP 是你自己的商品自己独立销售，平台仅提供收款服务。**

### PSP 选型时需要衡量的 7 项硬指标

- 安全与合规：PCI‑DSS Level 1、3‑D Secure 2、GDPR / CCPA。

- 集成方式：REST API、SDK、插件，是否有沙盒、文档深度。

- 全球覆盖：支持多少币种、国家、本地支付方式。

- 费率结构：本地卡费率 + 跨境附加费 + 货币转换费。

- 清算/结算：结算到企业账户的周期与币种。

- 可扩展性：并发、限额、支付路由、多 PSP 冗余。

- 客服与支持：24×7、技术对接窗口、拒付协助

### 做支付模块时还有一些注意事项

- 先把合规和商户资质准备好，再谈技术；否则 SDK 能跑但无法结算。

- 尽量让卡数据不落地（托管收银台 + 令牌化），把 PCI‑DSS 控制范围做到最小。

- 多 PSP / 路由不是越多越好，先满足目标市场 60%-80% 支付偏好，再逐步补充。

- 早期避免自建税务和拒付处理，能让你把注意力留给产品和增长。

## PSP 调研

### 调研内容

我们业务场景是需要自行与客户签约和销售，并且有自己的后台服务程序需要处理用户支付后的权益管理。所以适合上文所述半托管式。

根据上文分析，我将着重调研 *Stripe* 和  *PayPal*。

### 支付流程

作为研发人员，研发和对接一个系统，我们必须要理解其工作流程和原理。同时，我们要想说明白一件事，先直接放出流程图/时序图/框图最容易理解工作原理了。

这里，我避免直接复制 Stripe 的时序图过来。我将尝试以简化的时序图方式来让读者理解一般的 PSP 支付对接流程。

![PSP 支付对接流程](assets/psp-workflow.svg)

流程简单明了。其中，重中之重，我觉得是"履约"这一步，这一步是 PSP 回调我们的支付服务程序款项已经到账，请开始履约。**流程上没有收到这一步请求，所有的步骤都不认账!**

我们使用低代码(low-code)方式集成。这样既可以在自己的服务器中做事件通知和履约处理，也不需要自行定制付款页面。

## Stripe 调研

### 注册 Stripe 商户账户

- 到 https://stripe.com/ 注册一个账户
- 公司必须注册在 Stripe 支持的国家（推荐用香港、新加坡、美国公司）
- 填写公司信息、税号、银行账户
- 打开 Billing 模块（默认已启用）
- 开启 Stripe Tax（需要手动开启）

### 产品规划

- 你是否是一次性付费还是订阅制（会员、SaaS）
- 是否有试用期、优惠券
- 是否面向全球 → 是否要开启 Tax 功能（Stripe 会自动算全球税）

### 系统架构图

![系统架构图](assets/arch.svg)

其中，粉色背景框的程序属于支付模块。多实例避免单点。本模块中所有程序都是无状态，可随时启停和发版。

### 服务器端数据表设计

**订单表**

| 字段名                   | MySQL 类型                                                            | 说明 / 约束建议                 |
| --------------------- | ---------------------------------------------------------------------- | ------------------------- |
| id                    | `BIGINT UNSIGNED` **PRIMARY KEY AUTO\_INCREMENT**                      | 64 位整型主键                  |
| order\_id             | `VARCHAR(64)`                                                          | 订单 ID                     |
| user\_id              | `VARCHAR(64)`                                                          | 用户 ID                     |
| product\_id           | `VARCHAR(64)`                                                          | 产品 ID                     |
| product\_desc         | `TEXT`                                                                 | 产品描述（可改 `VARCHAR(255)` 等） |
| price\_id             | `VARCHAR(64)`                                                          | 价格 ID                     |
| quantity              | `INT UNSIGNED`                                                         | 购买数量                      |
| amount                | `DECIMAL(18,2)`                                                        | 订单总额（保留两位小数）              |
| net\_amount           | `DECIMAL(18,2)`                                                        | 去税/手续费后净额                 |
| currency              | `VARCHAR(3)`                                                              | ISO-4217 币种，例如 USD        |
| pay_status                | `ENUM('created','paid','succeeded','failed','expired','cancelled')`    | 支付状态                      |
| payed\_method         | `VARCHAR(32)`                                                          | 支付方式                      |
| psp\_provider         | `VARCHAR(32)`                                                          | PSP 名称                      |
| card\_number         | `VARCHAR(32)`                                                          | 支付卡尾号                      |
| payed\_at             | `DATETIME`                                                             | 支付时间                      |
| refund_status                | `ENUM('none','requested','succeeded','failed')`    | 退款状态                      |
| refunded\_at             | `DATETIME`                                                             | 退款时间                      |
| psp\_product\_id           | `VARCHAR(64)`                                                          | 产品 ID                     |
| psp\_product\_desc         | `TEXT`                                                                 | 产品描述（可改 `VARCHAR(255)` 等） |
| psp\_price\_id             | `VARCHAR(64)`                                                          | 价格 ID                     |
| psp\_payment\_id      | `VARCHAR(128)`                                                         | PSP 支付 ID                 |
| psp\_customer\_id     | `VARCHAR(128)`                                                         | PSP 客户 ID                 |
| psp\_customer\_email  | `VARCHAR(128)`                                                         | PSP 客户邮件                  |
| psp\_subscription\_id | `VARCHAR(128)`                                                         | PSP 订阅 ID                 |
| created\_at           | `DATETIME` **DEFAULT CURRENT\_TIMESTAMP**                              | 创建时间                      |
| updated\_at           | `DATETIME` **DEFAULT CURRENT\_TIMESTAMP ON UPDATE CURRENT\_TIMESTAMP** | 更新时间                      |
| deleted               | `TINYINT(1) UNSIGNED` **DEFAULT 0**                                    | 逻辑删除标记（0=未删）              |
| deleted\_at           | `DATETIME NULL`                                                        | 软删除时间                     |


sql 示意:

```sql
CREATE TABLE `order_items` (
  `id`                BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_id`          VARCHAR(64)  NOT NULL,
  `user_id`           VARCHAR(64)  NOT NULL,
  `product_id`        VARCHAR(64)  NOT NULL,
  `product_desc`      TEXT         NULL,
  `price_id`          VARCHAR(64)  NOT NULL,
  `quantity`          INT UNSIGNED NOT NULL DEFAULT 1,
  `amount`            DECIMAL(18,2) NOT NULL,
  `net_amount`        DECIMAL(18,2) NULL,
  `currency`          VARCHAR(3)      NOT NULL DEFAULT 'USD',
  `status`            ENUM('created','paid','succeeded','failed','expired','cancelled') NOT NULL DEFAULT 'created',
  `payed_method`      VARCHAR(32)  NULL,
  `card_number`      VARCHAR(32)  NULL,
  `payed_at`          DATETIME     NULL,
  `psp_product_id`    VARCHAR(64)  NOT NULL,
  `psp_product_desc`  TEXT         NULL,
  `psp_price_id`      VARCHAR(64)  NOT NULL,
  `psp_payment_id`    VARCHAR(128) NULL,
  `psp_customer_id`   VARCHAR(128) NULL,
  `psp_customer_email`VARCHAR(128) NULL,
  `psp_subscription_id`VARCHAR(128) NULL,
  `created_at`        DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at`        DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted`           TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
  `deleted_at`        DATETIME     NULL,
  PRIMARY KEY (`id`),
  KEY `idx_order` (`order_id`),
  KEY `idx_user`  (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

```

用户金融账户对于对接公司业务系统来说是透明的，这应该是合规的一部分。也就是说，业务系统是不接触用户信用卡信息的。

### 开发

开发运行前后端 demo 完成一次性付款 和 webhook。交易获取和订阅暂未验证。

**1. 集成方式**

Stripe 提供创建永久支付链接，这个是零代码付款，调研后认为我们没法集成到业务系统中，不适用我们产品，
https://dashboard.stripe.com/payment-links/create/customer-chooses-pricing

Stripe 还提供了托管表单（Checkout）和自定义集成（Elements）。我们用 Checkout 简化开发。

**2. 创建产品和价格**

Stripe 中支付是分两级结构，分别是产品和价格。可以建立多个产品，每个产品下面可以建多个价格。 每个价格可以设置是一次性付款还是订阅。

这里创建产品和价格，有两个方案:
- 方案1: 功能做在管理台中
- 方案2: 运营人员直接登录 Stripe 管理台中创建

另外，Stripe 是支持优惠券和折扣的。同时我们也可以做在我们的业务中，付款时直接按照优惠后的价格提交。这里可以 **TODO: 延缓确定**，后面我们在开发按照业务场景再确定。

用优惠券和促销码给客户的小计金额打折，降低向客户收取的金额:
https://docs.stripe.com/payments/checkout/discounts?payment-ui=stripe-hosted#create-a-coupon

**支付成功时顺便保存卡片 TODO:待验证**:

https://docs.stripe.com/payments/payment-intents#future-usage

https://docs.stripe.com/api/payment_intents/create#create_payment_intent-off_session

https://docs.stripe.com/payments/accept-a-payment?platform=web&ui=stripe-hosted#save-payment-methods-to-charge-them-off-session


**3. 前端发起支付**

![APP 工作流程图](assets/app-pay-flow.svg)

上图是支付时的工作流程图，粉色部分是我们需要开发的和支付相关的部分。PSP 网页不需要我们开发。

先判断本项权益是否已经购买，是否能(再次)购买。 此项**TODO: 待进一步确认**。

建议前端开发一个去付款的商品页面， 
![商品列表](assets/list.png)

这里需要后台对应的表中创建5个产品，每个产品再创建不同的价格分别对应不同国家。这里哪个微服务管理和存储这些数据，建议暂时放在绘本那个微服务中管理商品和各个国家的价格，因为后续相关的权益刷新需要根据商品来确认**TODO: 待确定。**

商品列表页点击"Go And Pay" 之后，服务器会返回 303 (http.StatusSeeOther)，http.Redirect 到第三方付款页面。

客户端调用 payment-backend `/api/v1/pay-service/checkout/session`，服务端调用 PSP `/v1/checkout/sessions` 生成一个订单插入数据库中，通过 PSP `/v1/checkout/sessions` 接口的 `client_reference_id` 字段传入订单号 `order_id。

`order_id` 的生成规则: 订单创建时间+amount_total+付款方式+雪花算法ID，

其中，
- 订单创建时间到"秒", 比如 20250626113045
- 货币(currency, "usd")

PSP `/v1/checkout/sessions` 接口会传入三个回调地址: 
- `success_url` 用于支付成功后跳转的地址，APP 显示 "感谢您的订阅/购买xxx"
- `cancel_url` 用于支付取消后跳转的地址，APP 显示 "欢迎您随时订阅/购买"
- `webhook_url` 用于支付成功后回调的地址，APP 不需要显示任何东西。

`success_url` 回调不能作为支付成功后履约的触发条件。因为 `success_url` 是否能收到不值得信任！

一共至少需要准备3个前端页面，分别是 `商品列表` 、`付款成功页面` 和 `付款取消页面`，示例如下，

![商品列表](assets/example-list.png)
![付款成功页面](assets/example-success.png)
![付款取消页面](assets/example-cancel.png)


**4. 后端监听支付成功 Webhook (用于激活权益)**

1. payment-backend 监听 PSP Webhook，订单成功时:

- 通过 `/api/v1/pay-service/checkout/webhook` `client_reference_id` 取出 `order_id`。
- payment-backend 再根据 `order_id` 更新必要的数据库状态字段。

这里有两种设计， 一是把 webhook 回调事件的数据入 MQ，然后同步程序在 MQ 取数据并处理；二是直接在 webhook 中处理，对于异常的数据交予 "订单同步程序" 处理。 建议初期采用方案二，实现简单稳妥，后期再考虑方案一。

webhook 回调不能作为支付成功后履约的唯一触发条件。因为 webhook 是否一定能收到也是不值得信任！

2. 调用微服务接口，更新用户权益(履约)。

**5. 订单同步程序(最终一致性保障, 用来兜底)**

依赖 Webhook 单点入库可能存在数据丢失风险。比如：
- 应用服务器短暂宕机、应用程序故障、网络波动和 DNS 故障等；
- Webhook 处理代码异常（例如数据库写入失败）；
- Stripe 尝试多次推送失败后不再重试（默认最多 3 天）。

Webhook 不可作为唯一信息来源，故而实现如下双保险机制:
- Webhook -> 实时快速入库（但可能失败）
- 后台定时查账 > 核对并补入库（最终一致性保障）

为了应对不一致，我们增加 payment-sync 程序。程序刚启动时以及定时(可依据需要配置6小时拉取)拉取 PSP 订单，同步到本地数据库。多个 payment-sync 实例可以通过 REDIS 分布式锁来保证只有一个实例执行，即便不是一个实例在运行导致同一条数据重复执行，所有其他微服务程序应该保证接口幂等性。也可以用 MQ 来实现多个实例同时工作，但是初期可以用上述单实例的简单方案先 run 起来。

主要逻辑如下，

-  查出近期本地订单（如配置最近 30 天，未标记为 paid/failed/canceled）
-  用  metadata或client_reference_id(**TODO: 待进一步研究确定**) 查询 PSP 接口订单状态
-  补写或更新数据库状态
-  调用微服务接口，更新用户权益(履约)

payment-sync 程序是最终保证一致性、支付成功的信任机制！

**记录详细的对账日志。**

**6. 销售订单**

除了支付业务外，这里还包含三部分数据(接口): 销售概要、销售订单和 支付记录。

销售概要、销售订单 由哪个微服务提供**TODO: 待确定**。

订单相关的接口: `/api/v1/pay-service/orders`

相应需求文档如下:

- 会员销售订单的字段有：会员的名称、会员的起始时间、会员的结束时间、会员的价格、购买会员的普通用户ID、销售员ID（如果是用户自己购买的显示“无”，销售员推送支付链接给用户付款时自动记录销售员的ID）、国家、货币单位,套餐类型，不同的国家不同的套餐，用户支付绑定月套餐，权益，剩余数量。
- 流量包销售订单字段：流量包的名称、购买流量包的普通用户ID，销售员的ID（如果是用户自己购买的显示“无”，销售员推送支付链接给用户付款时自动记录销售员的ID）

**7. 本地调试**

- 使用 Stripe 提供的 测试信用卡号
https://docs.stripe.com/testing#international-cards

- 测试订阅创建、失败、取消、续费等各种流程

目前只开发程序跑通了一次性付款，订阅**TODO: 待测试**。

### 测试

Sandbox 或 Live。

沙盒卡、SCA/3DS 流程、Webhook 重放、并发压测。

多币种测试。创建商品价格是美元，卖到欧元区，是否能支付。

对于支付后台，搜索并测试常见的攻击。

### 合规与发票、税务


| 模块       | Stripe 提供的能力                                     | 你要做的事                             |
| -------- | ------------------------------------------------ | --------------------------------- |
| **税务计算** | Stripe Tax 自动识别客户 IP/Billing 地址 → 计算应收 VAT / GST |  注册税务号（如 OSS / VAT ID）           |
| **发票开具** | Stripe Billing 自动生成 PDF 发票（带价格、税率）               |  填写发票抬头、税号；可邮件自动发给客户             |
| **税务申报** | ❌ Stripe 不报税（自己报）                               |  使用 Quaderno / Avalara / 会计师定期申报 |
| **合规策略** | Stripe 自动 PCI 合规、风控、3DS 验证                       |  设置 Terms of Use、退款条款等           |

### 上线

- 更换为 Live Key（sk_live_xxx / pk_live_xxx）

- 打开正式账户结算功能（验证税号、KYC）

- 检查 Pricing 是否设置正确币种

- 设置退款策略、隐私政策、结算周期（Stripe Dashboard 可配置）

**上线前检查表:**
https://docs.stripe.com/get-started/checklist/go-live

## PayPal 调研

开发运行前后端 demo 完成一次性付款。webhook、交易获取和订阅暂未验证。

### 工作流

PayPal 的 `系统架构图` 和 `工作流程图` 与 Stripe 的大体上相同。但是有一些细节和接口有较大不同。

PayPal 前端页面需要对接官方的 js sdk。

```javascript
<script
    src="https://www.paypal.com/sdk/js?client-id=test&buyer-country=US&currency=USD&components=buttons&enable-funding=venmo,paylater,card"
    data-sdk-integration-source="developer-studio"
></script>
```

1. 前端调创建订单 `POST /api/v1/pay-service/api/orders`，传入 `product_id` 和 `product_quantity`

2. 后端调用 PayPal `/v2/checkout/orders` 创建订单，传入 `currencyCode`(货币) 和 `value`(总额)

3. 创建订单后有返回 `approval_url` 引导页面跳转到 P2P 付款页面。用户付款。

4. PSP 调用前端页面的 `onApprove` 方法。

5. 前端调后端的 `POST /api/v1/pay-service/orders/capture/{orderID}`。 这里相当于 Stripe 的 `success_url` `cancel_url` 被调用。成功后，订单状态为 "COMPLETED"，你可以从响应中提取交易号、付款状态等。

### webhook 

配置 webhook，监听 CHECKOUT.ORDER.APPROVED、PAYMENT.CAPTURE.COMPLETED 等事件。

1. 找到应用下的 Webhook 区域。点击 Add Webhook，如 https://domain.com/api/v1/pay-service/paypal/webhook

2. 选择事件类型

| 事件类型                                 | 说明                     |
| ------------------------------------ | ---------------------- |
| `CHECKOUT.ORDER.APPROVED`            | 用户在 PayPal 页面点击了“同意付款” |
| `PAYMENT.CAPTURE.COMPLETED`          | 实际完成付款（钱扣走）   |
| `PAYMENT.CAPTURE.DENIED`             | 扣款失败                   |
| `PAYMENT.CAPTURE.REFUNDED`           | 用户退款                   |
| `BILLING.SUBSCRIPTION.CREATED`（如有订阅） | 创建订阅                   |
| `BILLING.SUBSCRIPTION.CANCELLED`     | 用户取消订阅                 |

### 查询订单

- 查询订单详情
- 查询付款详情

**TODO: 待完成**

## 结论

综合运行代码调研来看，正如网上所言，Stripe 的文档和功能都相当完善，而 PayPal 即使有相应的功能，文档也不细致、不全面亦或是功能集成起来繁琐。PayPal 在集成和发布运行方面的可把握程度方面逊于 Stripe。

本期在有限的时间和研发、测试人力情况下，建议先集成 Stripe 这一个支付网关，初期不求多，但求稳。虽然不能 100% 满足所有支付手段，但是可以让所有用户都能支付得起来。后期可以根据用户反馈情况再集成 Stripe+PayPal 双通道甚至多通道支付网关。

## 附录

### 关于拒付的说明

https://docs.stripe.com/declines

**支付尤其在海外支付可能会涉及一些法律法规，可能需要咨询专业的律师来合规。**

### 付款佣金对比

https://stripe.com/zh-us/pricing

https://www.paypal.com/c2/business/paypal-business-fees#statement-1

### 提现手续费对比

Stripe 不会向您收取正常提现的费用。
https://docs.stripe.com/payouts?locale=zh-CN#payout-fees

从企业PayPal账户提出余额
美国银行账户	35.00 USD（每笔提现）
https://www.paypal.com/c2/business/paypal-business-fees#statement-2

在沙箱中测试了一下提款 100美元 到银行卡:
可用余额US$5,096.30
费用$20.00 USD费用

### 支持的付款方式对比

支持的货币/国家
https://docs.stripe.com/currencies
https://www.paypal.com/c2/webapps/mpp/country-worldwide


支持的支付方式
strip 支持 Apple Pay/Google Pay/WeChat Pay(美国)/Alipay(美国)
https://docs.stripe.com/payments/payment-methods/payment-method-support#country-currency-support
https://dashboard.stripe.com/test/settings/payment_methods

### stripe 对于 paypal 的支持

https://docs.stripe.com/payments/paypal

### Webhook 用法最佳实践
https://docs.stripe.com/webhooks?locale=zh-CN&snapshot-or-thin=snapshot&dashboard-or-api=dashboard#best-practices
