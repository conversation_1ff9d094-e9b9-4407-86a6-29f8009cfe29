package server

import (
	"context"
	"fmt"

	"dubbo.apache.org/dubbo-go/v3"
	"dubbo.apache.org/dubbo-go/v3/protocol"
	"dubbo.apache.org/dubbo-go/v3/registry"
	"dubbo.apache.org/dubbo-go/v3/server"

	"payment-backend/internal/config"
	dubboHandler "payment-backend/internal/dubbo/handler"
	"payment-backend/internal/dubbo/shared-protos/gen/go/paymentpb"
	"payment-backend/internal/logger"

	// 导入所有必要的dubbo-go插件
	_ "dubbo.apache.org/dubbo-go/v3/cluster/cluster/adaptivesvc"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/cluster/available"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/cluster/broadcast"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/cluster/failback"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/cluster/failfast"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/cluster/failover"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/cluster/failsafe"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/cluster/forking"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/cluster/zoneaware"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/loadbalance/aliasmethod"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/loadbalance/consistenthashing"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/loadbalance/iwrr"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/loadbalance/leastactive"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/loadbalance/p2c"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/loadbalance/random"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/loadbalance/roundrobin"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/router/condition"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/router/meshrouter"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/router/polaris"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/router/script"
	_ "dubbo.apache.org/dubbo-go/v3/cluster/router/tag"
	_ "dubbo.apache.org/dubbo-go/v3/config_center/nacos"
	_ "dubbo.apache.org/dubbo-go/v3/config_center/zookeeper"
	_ "dubbo.apache.org/dubbo-go/v3/filter/accesslog"
	_ "dubbo.apache.org/dubbo-go/v3/filter/active"
	_ "dubbo.apache.org/dubbo-go/v3/filter/adaptivesvc"
	_ "dubbo.apache.org/dubbo-go/v3/filter/auth"
	_ "dubbo.apache.org/dubbo-go/v3/filter/echo"
	_ "dubbo.apache.org/dubbo-go/v3/filter/exec_limit"
	_ "dubbo.apache.org/dubbo-go/v3/filter/generic"
	_ "dubbo.apache.org/dubbo-go/v3/filter/graceful_shutdown"
	_ "dubbo.apache.org/dubbo-go/v3/filter/hystrix"
	_ "dubbo.apache.org/dubbo-go/v3/filter/metrics"
	_ "dubbo.apache.org/dubbo-go/v3/filter/otel/trace"
	_ "dubbo.apache.org/dubbo-go/v3/filter/polaris/limit"
	_ "dubbo.apache.org/dubbo-go/v3/filter/seata"
	_ "dubbo.apache.org/dubbo-go/v3/filter/sentinel"
	_ "dubbo.apache.org/dubbo-go/v3/filter/token"
	_ "dubbo.apache.org/dubbo-go/v3/filter/tps"
	_ "dubbo.apache.org/dubbo-go/v3/filter/tps/limiter"
	_ "dubbo.apache.org/dubbo-go/v3/filter/tps/strategy"
	_ "dubbo.apache.org/dubbo-go/v3/filter/tracing"
	_ "dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata"
	_ "dubbo.apache.org/dubbo-go/v3/metadata/report/etcd"
	_ "dubbo.apache.org/dubbo-go/v3/metadata/report/nacos"
	_ "dubbo.apache.org/dubbo-go/v3/metadata/report/zookeeper"
	_ "dubbo.apache.org/dubbo-go/v3/metrics/app_info"
	_ "dubbo.apache.org/dubbo-go/v3/metrics/prometheus"
	_ "dubbo.apache.org/dubbo-go/v3/otel/trace/jaeger"
	_ "dubbo.apache.org/dubbo-go/v3/otel/trace/stdout"
	_ "dubbo.apache.org/dubbo-go/v3/otel/trace/zipkin"
	_ "dubbo.apache.org/dubbo-go/v3/protocol/dubbo"
	_ "dubbo.apache.org/dubbo-go/v3/protocol/jsonrpc"
	_ "dubbo.apache.org/dubbo-go/v3/protocol/rest"
	_ "dubbo.apache.org/dubbo-go/v3/protocol/triple"
	_ "dubbo.apache.org/dubbo-go/v3/protocol/triple/health"
	_ "dubbo.apache.org/dubbo-go/v3/protocol/triple/reflection"
	_ "dubbo.apache.org/dubbo-go/v3/proxy/proxy_factory"
	_ "dubbo.apache.org/dubbo-go/v3/registry/directory"
	_ "dubbo.apache.org/dubbo-go/v3/registry/etcdv3"
	_ "dubbo.apache.org/dubbo-go/v3/registry/nacos"
	_ "dubbo.apache.org/dubbo-go/v3/registry/polaris"
	_ "dubbo.apache.org/dubbo-go/v3/registry/protocol"
	_ "dubbo.apache.org/dubbo-go/v3/registry/servicediscovery"
	_ "dubbo.apache.org/dubbo-go/v3/registry/xds"
	_ "dubbo.apache.org/dubbo-go/v3/registry/zookeeper"
	_ "dubbo.apache.org/dubbo-go/v3/xds/client/controller/version/v2"
	_ "dubbo.apache.org/dubbo-go/v3/xds/client/controller/version/v3"
)

// DubboServer Dubbo服务器
type DubboServer struct {
	server *server.Server
	logger logger.Logger
	config *config.Config
}

// NewDubboServer 创建Dubbo服务器
func NewDubboServer(
	cfg *config.Config,
	orderDubboHandler *dubboHandler.OrderDubboHandler,
	logger logger.Logger,
) (*DubboServer, error) {
	// 创建Dubbo实例
	ins, err := dubbo.NewInstance(
		dubbo.WithName("payment-service"),
		dubbo.WithRegistry(
			registry.WithNacos(),
			registry.WithAddress(cfg.Dubbo.Registry.Address),
			registry.WithNamespace(cfg.Dubbo.Registry.Namespace),
			registry.WithUsername(cfg.Dubbo.Registry.Username),
			registry.WithPassword(cfg.Dubbo.Registry.Password),
		),
		dubbo.WithProtocol(
			protocol.WithTriple(),
			protocol.WithPort(cfg.Dubbo.Port),
			protocol.WithIp(cfg.Dubbo.IP),
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create dubbo instance: %w", err)
	}

	// 创建服务器
	server, err := ins.NewServer()
	if err != nil {
		return nil, fmt.Errorf("failed to create dubbo server: %w", err)
	}

	// 注册服务
	if err := paymentpb.RegisterOrderServiceHandler(server, orderDubboHandler); err != nil {
		return nil, fmt.Errorf("failed to register order service: %w", err)
	}

	return &DubboServer{
		server: server,
		logger: logger,
		config: cfg,
	}, nil
}

// Start 启动Dubbo服务器
func (s *DubboServer) Start() error {
	s.logger.Info("Starting Dubbo server",
		logger.String("port", fmt.Sprintf("%d", s.config.Dubbo.Port)),
		logger.String("ip", s.config.Dubbo.IP))

	if err := s.server.Serve(); err != nil {
		return fmt.Errorf("dubbo server serve failed: %w", err)
	}

	return nil
}

// Stop 停止Dubbo服务器
func (s *DubboServer) Stop(ctx context.Context) error {
	s.logger.Info("Stopping Dubbo server")
	// Dubbo-go v3 的服务器停止逻辑
	return nil
}
