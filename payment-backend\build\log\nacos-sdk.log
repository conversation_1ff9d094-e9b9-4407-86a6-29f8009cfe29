2025-07-14T09:39:59.709+0800	ERROR	nacos_server/nacos_server.go:89	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-14T09:39:59.713+0800	INFO	nacos_client/nacos_client.go:65	logDir:<C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\log> cacheDir:<C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache>
2025-07-14T09:39:59.714+0800	ERROR	nacos_server/nacos_server.go:89	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-14T09:39:59.717+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55624
2025-07-14T09:39:59.717+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] 49e0af64-5c0a-45fc-814c-720635fe7bf4 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-14T09:39:59.773+0800	INFO	util/common.go:96	Local IP:************
2025-07-14T09:39:59.773+0800	WARN	rpc/rpc_client.go:226	[RpcClient.Start] 49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it.", start up retry times left=2
2025-07-14T09:39:59.773+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] 49e0af64-5c0a-45fc-814c-720635fe7bf4 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-14T09:39:59.774+0800	WARN	rpc/rpc_client.go:226	[RpcClient.Start] 49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it.", start up retry times left=1
2025-07-14T09:39:59.774+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] 49e0af64-5c0a-45fc-814c-720635fe7bf4 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-14T09:39:59.774+0800	WARN	rpc/rpc_client.go:226	[RpcClient.Start] 49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it.", start up retry times left=0
2025-07-14T09:39:59.774+0800	INFO	rpc/rpc_client.go:324	49e0af64-5c0a-45fc-814c-720635fe7bf4 try to re connect to a new server, server is not appointed, will choose a random server.
2025-07-14T09:39:59.775+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-14T09:39:59.776+0800	WARN	rpc/rpc_client.go:226	[RpcClient.Start] config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it.", start up retry times left=2
2025-07-14T09:39:59.776+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-14T09:39:59.776+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 1 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:39:59.776+0800	WARN	rpc/rpc_client.go:226	[RpcClient.Start] config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it.", start up retry times left=1
2025-07-14T09:39:59.776+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-14T09:39:59.776+0800	WARN	rpc/rpc_client.go:226	[RpcClient.Start] config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it.", start up retry times left=0
2025-07-14T09:39:59.777+0800	INFO	rpc/rpc_client.go:324	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 try to re connect to a new server, server is not appointed, will choose a random server.
2025-07-14T09:39:59.777+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:39:59.778+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 1 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:39:59.877+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 2 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:39:59.877+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:39:59.878+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 2 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:39:59.977+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.078+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 3 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:00.078+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:00.078+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:00.078+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.078+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 3 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:00.179+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.279+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.379+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 4 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:00.379+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 4 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:00.380+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.480+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.581+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.681+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:00.681+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:00.681+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.781+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 5 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:00.781+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 5 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:00.781+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.882+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.982+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.082+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.183+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.281+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 6 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:01.281+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 6 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:01.283+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:01.283+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:01.283+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.384+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.484+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.584+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.685+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.785+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.881+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 7 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:01.881+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 7 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:01.886+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:01.886+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:01.886+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.987+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.087+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.188+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.289+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.389+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.489+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:02.489+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:02.489+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.582+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 8 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:02.582+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 8 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:02.590+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.690+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.790+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.891+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.991+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.091+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:03.091+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:03.091+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.192+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.292+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.383+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 9 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:03.383+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 9 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:03.392+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.492+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.593+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.693+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:03.694+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:03.694+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.794+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.894+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.995+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.095+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.195+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.285+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 10 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:04.285+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 10 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:04.296+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:04.296+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:04.296+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.396+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.497+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.597+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.698+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.712+0800	ERROR	security/security_proxy.go:90	login has error Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-14T09:40:04.717+0800	ERROR	security/security_proxy.go:90	login has error Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-14T09:40:04.799+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.899+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:04.899+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:04.899+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.000+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.100+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.201+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.286+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 11 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:05.286+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 11 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:05.301+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.402+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.502+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:05.502+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:05.502+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.602+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.702+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:06.386+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 12 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:06.386+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 12 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:07.586+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 13 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:07.586+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 13 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:08.887+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 14 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:08.887+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 14 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
