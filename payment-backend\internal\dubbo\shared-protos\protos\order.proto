syntax = "proto3";

package com.aibook.payment.grpc;
option java_multiple_files = true;
option go_package = "./paymentpb;paymentpb";

import "google/protobuf/timestamp.proto";

// 订单信息
message Order {
  uint64 id = 1;
  string order_id = 2;
  string user_id = 3;
  string product_id = 4;
  string product_desc = 5;
  string price_id = 6;
  uint32 quantity = 7;
  double amount = 8;
  double net_amount = 9;
  string currency = 10;
  string pay_status = 11;
  string payed_method = 12;
  string psp_provider = 13;
  string card_number = 14;
  optional google.protobuf.Timestamp payed_at = 15;
  string refund_status = 16;
  optional google.protobuf.Timestamp refunded_at = 17;
  string psp_product_id = 18;
  string psp_product_desc = 19;
  string psp_price_id = 20;
  string psp_payment_id = 21;
  string psp_customer_id = 22;
  string psp_customer_email = 23;
  string psp_subscription_id = 24;
  google.protobuf.Timestamp created_at = 25;
  google.protobuf.Timestamp updated_at = 26;
  bool deleted = 27;
  optional google.protobuf.Timestamp deleted_at = 28;
}

// 订单过滤条件
message OrderFilter {
  optional string user_id = 1;
  optional string currency = 2;
  optional string pay_status = 3;
  optional string payed_method = 4;
  optional string psp_provider = 5;
  optional google.protobuf.Timestamp payed_at_start = 6;
  optional google.protobuf.Timestamp payed_at_end = 7;
  optional string refund_status = 8;
  optional google.protobuf.Timestamp refunded_at_start = 9;
  optional google.protobuf.Timestamp refunded_at_end = 10;
  optional string psp_price_id = 11;
  optional string psp_customer_email = 12;
  optional string psp_subscription_id = 13;
}

// 分页请求
message PaginationRequest {
  int32 limit = 1;
  int32 offset = 2;
}

// 分页响应
message PaginationResponse {
  int64 total = 1;
  int32 limit = 2;
  int32 offset = 3;
  int64 remaining = 4;
}

// 获取所有订单请求
message ListAllOrdersRequest {
  optional OrderFilter filter = 1;
  optional PaginationRequest pagination = 2;
}

// 获取所有订单响应
message ListAllOrdersResponse {
  repeated Order orders = 1;
  PaginationResponse pagination = 2;
}

// 订单服务定义
service OrderService {
  // 获取所有订单列表（管理员接口）
  rpc ListAllOrders(ListAllOrdersRequest) returns (ListAllOrdersResponse);
}
