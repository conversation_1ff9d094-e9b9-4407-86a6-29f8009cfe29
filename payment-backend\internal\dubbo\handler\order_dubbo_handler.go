package handler

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"payment-backend/internal/domain"
	"payment-backend/internal/dubbo/shared-protos/gen/go/paymentpb"
	"payment-backend/internal/logger"
)

// OrderDubboHandler Dubbo订单处理器
type OrderDubboHandler struct {
	orderService domain.OrderService
	logger       logger.Logger
}

// NewOrderDubboHandler 创建Dubbo订单处理器
func NewOrderDubboHandler(orderService domain.OrderService, logger logger.Logger) *OrderDubboHandler {
	return &OrderDubboHandler{
		orderService: orderService,
		logger:       logger,
	}
}

// ListAllOrders 获取所有订单列表（管理员接口）
func (h *OrderDubboHandler) ListAllOrders(ctx context.Context, req *paymentpb.ListAllOrdersRequest) (*paymentpb.ListAllOrdersResponse, error) {
	h.logger.Info("Dubbo ListAllOrders called",
		zap.Any("filter", req.Filter),
		zap.Any("pagination", req.Pagination))

	// 转换过滤条件
	filter := h.convertFilterFromProto(req.Filter)

	// 转换分页参数
	pagination := h.convertPaginationFromProto(req.Pagination)

	// 调用业务服务层
	response, err := h.orderService.ListAllOrders(filter, pagination)
	if err != nil {
		h.logger.Error("Failed to list all orders via Dubbo",
			zap.Any("filter", filter),
			zap.Any("pagination", pagination),
			zap.Error(err))
		return nil, err
	}

	// 转换响应
	protoResponse := h.convertResponseToProto(response)

	h.logger.Info("Dubbo ListAllOrders completed successfully",
		zap.Int("order_count", len(protoResponse.Orders)),
		zap.Int64("total", protoResponse.Pagination.Total))

	return protoResponse, nil
}

// convertFilterFromProto 转换过滤条件从Proto到Domain
func (h *OrderDubboHandler) convertFilterFromProto(protoFilter *paymentpb.OrderFilter) *domain.OrderFilter {
	if protoFilter == nil {
		return &domain.OrderFilter{}
	}

	filter := &domain.OrderFilter{}

	if protoFilter.UserId != nil {
		filter.UserID = protoFilter.UserId
	}
	if protoFilter.Currency != nil {
		filter.Currency = protoFilter.Currency
	}
	if protoFilter.PayStatus != nil {
		filter.PayStatus = protoFilter.PayStatus
	}
	if protoFilter.PayedMethod != nil {
		filter.PayedMethod = protoFilter.PayedMethod
	}
	if protoFilter.PspProvider != nil {
		filter.PSPProvider = protoFilter.PspProvider
	}
	if protoFilter.PayedAtStart != nil {
		t := protoFilter.PayedAtStart.AsTime()
		filter.PayedAtStart = &t
	}
	if protoFilter.PayedAtEnd != nil {
		t := protoFilter.PayedAtEnd.AsTime()
		filter.PayedAtEnd = &t
	}
	if protoFilter.RefundStatus != nil {
		filter.RefundStatus = protoFilter.RefundStatus
	}
	if protoFilter.RefundedAtStart != nil {
		t := protoFilter.RefundedAtStart.AsTime()
		filter.RefundedAtStart = &t
	}
	if protoFilter.RefundedAtEnd != nil {
		t := protoFilter.RefundedAtEnd.AsTime()
		filter.RefundedAtEnd = &t
	}
	if protoFilter.PspPriceId != nil {
		filter.PSPPriceID = protoFilter.PspPriceId
	}
	if protoFilter.PspCustomerEmail != nil {
		filter.PSPCustomerEmail = protoFilter.PspCustomerEmail
	}
	if protoFilter.PspSubscriptionId != nil {
		filter.PSPSubscriptionID = protoFilter.PspSubscriptionId
	}

	return filter
}

// convertPaginationFromProto 转换分页参数从Proto到Domain
func (h *OrderDubboHandler) convertPaginationFromProto(protoPagination *paymentpb.PaginationRequest) *domain.PaginationRequest {
	if protoPagination == nil {
		return &domain.PaginationRequest{
			Limit:  50, // 默认值
			Offset: 0,
		}
	}

	return &domain.PaginationRequest{
		Limit:  int(protoPagination.Limit),
		Offset: int(protoPagination.Offset),
	}
}

// convertResponseToProto 转换响应从Domain到Proto
func (h *OrderDubboHandler) convertResponseToProto(response *domain.ListOrdersResponse) *paymentpb.ListAllOrdersResponse {
	protoOrders := make([]*paymentpb.Order, len(response.Orders))
	for i, order := range response.Orders {
		protoOrders[i] = h.convertOrderToProto(order)
	}

	return &paymentpb.ListAllOrdersResponse{
		Orders: protoOrders,
		Pagination: &paymentpb.PaginationResponse{
			Total:     response.Pagination.Total,
			Limit:     int32(response.Pagination.Limit),
			Offset:    int32(response.Pagination.Offset),
			Remaining: response.Pagination.Remaining,
		},
	}
}

// convertOrderToProto 转换订单从Domain到Proto
func (h *OrderDubboHandler) convertOrderToProto(order *domain.Order) *paymentpb.Order {
	protoOrder := &paymentpb.Order{
		Id:                order.ID,
		OrderId:           order.OrderID,
		UserId:            order.UserID,
		ProductId:         order.ProductID,
		ProductDesc:       order.ProductDesc,
		PriceId:           order.PriceID,
		Quantity:          order.Quantity,
		Amount:            order.Amount,
		NetAmount:         order.NetAmount,
		Currency:          order.Currency,
		PayStatus:         order.PayStatus,
		PayedMethod:       order.PayedMethod,
		PspProvider:       order.PSPProvider,
		CardNumber:        order.CardNumber,
		RefundStatus:      order.RefundStatus,
		PspProductId:      order.PSPProductID,
		PspProductDesc:    order.PSPProductDesc,
		PspPriceId:        order.PSPPriceID,
		PspPaymentId:      order.PSPPaymentID,
		PspCustomerId:     order.PSPCustomerID,
		PspCustomerEmail:  order.PSPCustomerEmail,
		PspSubscriptionId: order.PSPSubscriptionID,
		CreatedAt:         timestamppb.New(order.CreatedAt),
		UpdatedAt:         timestamppb.New(order.UpdatedAt),
		Deleted:           order.Deleted,
	}

	if order.PayedAt != nil {
		protoOrder.PayedAt = timestamppb.New(*order.PayedAt)
	}
	if order.RefundedAt != nil {
		protoOrder.RefundedAt = timestamppb.New(*order.RefundedAt)
	}
	if order.DeletedAt != nil {
		protoOrder.DeletedAt = timestamppb.New(*order.DeletedAt)
	}

	return protoOrder
}
